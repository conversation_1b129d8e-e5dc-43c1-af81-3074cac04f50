//参考文章：https://blog.csdn.net/u011625956/article/details/136828317
//https://www.instructables.com/<PERSON><PERSON>Bros-Clock/
//https://github.com/jnthas/mariobros-clock

#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include "Clockface.h"
#include "WiFiConnect.h"
#include "CWDateTime.h"
#include "WebServer.h"

// 🔌 Pin Definitions (ESP32-S3 to HUB75 RGB Matrix)
// ---------------------------
#define R1_PIN 47
#define G1_PIN 1
#define B1_PIN 48
#define R2_PIN 45
#define G2_PIN 2
#define B2_PIN 0  // ⚠️ GPIO0 is BOOT pin, must stay HIGH at boot

#define A_PIN 35
#define B_PIN 41
#define C_PIN 36
#define D_PIN 40
#define E_PIN 42

#define LAT_PIN 39
#define OE_PIN 38
#define CL<PERSON>_PIN 37

// ---------------------------
// 🧱 Panel Configuration
// ---------------------------
#define PANEL_RES_X 64
#define PANEL_RES_Y 64
#define PANEL_CHAIN 1

MatrixPanel_I2S_DMA *dma_display = nullptr;
WiFiConnect wifi;
CWDateTime cwDateTime;
Clockface *clockface;
ClockWebServer *webServer;

// Color variables - will be initialized after display setup
uint16_t myBLACK;
uint16_t myWHITE;
uint16_t myBLUE;

byte displayBright = 32;

void displaySetup() {
  HUB75_I2S_CFG mxconfig(
    PANEL_RES_X,   // module width
    PANEL_RES_Y,   // module height
    PANEL_CHAIN    // Chain length
  );

  // Pin mapping
  mxconfig.gpio.r1 = R1_PIN;
  mxconfig.gpio.g1 = G1_PIN;
  mxconfig.gpio.b1 = B1_PIN;
  mxconfig.gpio.r2 = R2_PIN;
  mxconfig.gpio.g2 = G2_PIN;
  mxconfig.gpio.b2 = B2_PIN;
  mxconfig.gpio.a = A_PIN;
  mxconfig.gpio.b = B_PIN;
  mxconfig.gpio.c = C_PIN;
  mxconfig.gpio.d = D_PIN;
  mxconfig.gpio.e = E_PIN;
  mxconfig.gpio.lat = LAT_PIN;
  mxconfig.gpio.oe = OE_PIN;
  mxconfig.gpio.clk = CLK_PIN;

  mxconfig.clkphase = false;

  // Display Setup
  dma_display = new MatrixPanel_I2S_DMA(mxconfig);
  dma_display->begin();
  dma_display->setBrightness8(displayBright);

  // Initialize colors after display is created
  myBLACK = dma_display->color565(0, 0, 0);
  myWHITE = dma_display->color565(255, 255, 255);
  myBLUE = dma_display->color565(0, 0, 255);

  dma_display->clearScreen();
  dma_display->fillScreen(myBLACK);
}

void setup() {

  Serial.begin(115200);

  displaySetup();

  clockface = new Clockface(dma_display);

  dma_display->setTextSize(1);
  dma_display->setTextColor(myWHITE);
  dma_display->setCursor(5, 0);
  dma_display->println("CLOCKWISE");
  dma_display->setTextColor(myBLUE);
  dma_display->setCursor(0, 32);
  dma_display->print("connecting...");

  wifi.connect();
  cwDateTime.begin();

  clockface->setup(&cwDateTime);
  
  // Initialize web server
  webServer = new ClockWebServer(clockface);
  webServer->begin();
  
  Serial.println("Setup complete!");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());
}

void loop() {
  clockface->update();
  webServer->handleClient();
}
